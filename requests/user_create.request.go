package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type UserCreate struct {
	core.BaseValidator
	Email       *string `json:"email"`
	FullName    *string `json:"full_name"`
	DisplayName *string `json:"display_name"`
	Position    *string `json:"position"`
	TeamCode    *string `json:"team_code"`
	AvatarURL   *string `json:"avatar_url"`
	Role        *string `json:"role"`
}

func (r *UserCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>mail(r.<PERSON>ail, "email"))
	r.Must(r.<PERSON>equired(r.<PERSON>ail, "email"))
	r.Must(r.Is<PERSON>trUnique(ctx, r.Email, models.User{}.TableName(), "email", "", "email"))

	r.Must(r.<PERSON>equired(r.<PERSON>, "full_name"))

	r.Must(r.<PERSON>xi<PERSON>(ctx, r.<PERSON>, models.Team{}.TableN<PERSON>(), "code", "team_code"))
	r.Must(r.<PERSON>n(r.<PERSON>,
		strings.Join([]string{
			string(models.UserRoleAdmin),
			string(models.UserRoleUser),
			string(models.UserRoleSuperAdmin),
		}, "|"),
		"role"))
	r.Must(r.IsURL(r.AvatarURL, "avatar_url"))
	return r.Error()
}
