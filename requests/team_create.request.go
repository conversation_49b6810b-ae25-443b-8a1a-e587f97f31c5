package requests

import (
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type TeamCreate struct {
	core.BaseValidator
	Name           *string `json:"name"`
	Code           *string `json:"code"`
	Description    *string `json:"description"`
	StartWorkingAt *string `json:"start_working_at"`
	EndWorkingAt   *string `json:"end_working_at"`
}

func (r *TeamCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrUnique(ctx, r.Name, models.Team{}.TableName(), "name", "", "name"))
	r.Must(r.IsStrUnique(ctx, r.Code, models.Team{}.TableName(), "code", "", "code"))

	if r.StartWorkingAt != nil {
		if _, err := time.Parse("15:04", *r.StartWorkingAt); err != nil {
			r.Must(false, &core.IValidMessage{
				Name:    "start_working_at",
				Code:    "INVALID_TIME_FORMAT",
				Message: "Start working time must be in HH:MM format",
			})
		}
	}

	if r.<PERSON>WorkingAt != nil {
		if _, err := time.Parse("15:04", *r.EndWorkingAt); err != nil {
			r.Must(false, &core.IValidMessage{
				Name:    "end_working_at",
				Code:    "INVALID_TIME_FORMAT",
				Message: "End working time must be in HH:MM format",
			})
		}
	}

	// Validate working hours if both are provided
	if r.StartWorkingAt != nil && r.EndWorkingAt != nil {
		startTime, err1 := time.Parse("15:04", *r.StartWorkingAt)
		endTime, err2 := time.Parse("15:04", *r.EndWorkingAt)

		if err1 == nil && err2 == nil {
			if startTime.After(endTime) || startTime.Equal(endTime) {
				r.Must(false, &core.IValidMessage{
					Name:    "start_working_at",
					Code:    "INVALID_WORKING_HOURS",
					Message: "Start working time must be before end working time",
				})
			}
		}
	}

	return r.Error()
}
