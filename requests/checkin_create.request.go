package requests

import (
	"strings"
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type CheckinCreate struct {
	core.BaseValidator
	Type        *string `json:"type"`
	LeavePeriod *string `json:"leave_period"`
	Location    *string `json:"location"`
	Remarks     *string `json:"remarks"`
	IsUnused    *bool   `json:"is_unused"`
	Date        *string `json:"date"`
}

func (r *CheckinCreate) Valid(ctx core.IContext) core.IError {

	if r.Must(r.IsStrRequired(r.Type, "type")) {
		if utils.ToNonPointer(r.Type) == string(models.CheckinTypeOnsite) {
			r.Must(r.IsStrRequired(r.Location, "location"))
			r.Must(r.IsStrIn(r.Type, strings.Join([]string{string(models.CheckinTypeOffice), string(models.CheckinTypeWfh),
				string(models.CheckinTypeOnsite)}, "|"), "type"))
		}
	}
	if r.Must(r.Is<PERSON>equired(r.<PERSON>, "leave_period")) {
		r.Must(r.IsStrIn(r.LeavePeriod, strings.Join([]string{string(models.LeavePeriodHalfMorning), string(models.LeavePeriodHalfAfternoon),
			string(models.LeavePeriodFullDay), string(models.LeavePeriodManyDays)}, "|"), "leave_period"))
	}

	r.Must(r.IsRequired(r.Date, "date"))
	if r.Date != nil {
		if _, err := time.Parse("15:04", *r.Date); err != nil {
			r.Must(false, &core.IValidMessage{
				Name:    "date",
				Code:    "INVALID_TIME_FORMAT",
				Message: "End working time must be in HH:MM format",
			})
		}
	}

	return r.Error()
}
