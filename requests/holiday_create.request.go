package requests

import (
	"time"

	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type HolidayCreate struct {
	core.BaseValidator
	Name       *string    `json:"name"`
	Date       *time.Time `json:"date"`
	IsNational *bool      `json:"is_national"`
}

func (r *HolidayCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Name, "name"))
	r.Must(r.IsRequired(r.Date, "date"))

	if r.IsNational == nil {
		// Set default value if not provided
		r.IsNational = utils.ToPointer(false)
	}

	return r.Error()
}
