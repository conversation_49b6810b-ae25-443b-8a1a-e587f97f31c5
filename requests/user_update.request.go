package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type UserUpdate struct {
	core.BaseValidator
	FullName    *string `json:"full_name"`
	DisplayName *string `json:"display_name"`
	Position    *string `json:"position"`
	TeamCode    *string `json:"team_code"`
	AvatarURL   *string `json:"avatar_url"`
	Role        *string `json:"role"`
}

func (r *UserUpdate) Valid(ctx core.IContext) core.IError {
	// All fields are optional for updates
	r.Must(r.IsExists(ctx, r.TeamCode, models.Team{}.TableName(), "code", "team_code"))
	r.Must(r.IsStrIn(r.Role,
		strings.Join([]string{
			string(models.UserRoleAdmin),
			string(models.UserRoleUser),
			string(models.UserRoleSuperAdmin),
		}, "|"),
		"role"))
	r.Must(r.IsURL(r.AvatarURL, "avatar_url"))
	return r.<PERSON>rror()
}
