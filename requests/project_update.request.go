package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
)

type ProjectUpdate struct {
	core.BaseValidator
	Name        *string `json:"name"`
	Code        *string `json:"code"`
	Description *string `json:"description"`
}

func (r *ProjectUpdate) Valid(ctx core.IContext) core.IError {
	cc := ctx.(core.IHTTPContext)
	oldCode := ""
	oldName := ""
	project, _ := repo.Project(cc).FindOne("id = ?", cc.Param("id"))
	if project != nil {
		oldCode = project.Code
		oldName = project.Name
	}

	r.Must(r.IsStrUnique(ctx, r.Name, models.Team{}.TableName(), "name", oldName, "name"))
	r.Must(r.IsStrUnique(ctx, r.Code, models.Team{}.TableName(), "code", oldCode, "code"))

	return r.Error()
}
