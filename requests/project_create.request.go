package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type ProjectCreate struct {
	core.BaseValidator
	Name           *string `json:"name"`
	Code           *string `json:"code"`
	Description    *string `json:"description"`
	StartWorkingAt *string `json:"start_working_at"`
	EndWorkingAt   *string `json:"end_working_at"`
}

func (r *ProjectCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Name, "name"))
	r.Must(r.IsStrRequired(r.Code, "code"))
	r.Must(r.IsStrUnique(ctx, r.Name, models.Project{}.TableName(), "name", "", "name"))
	r.Must(r.IsStrUnique(ctx, r.Code, models.Project{}.TableName(), "code", "", "code"))

	return r.<PERSON>rror()
}
