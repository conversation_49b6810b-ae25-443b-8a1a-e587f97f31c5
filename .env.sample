HOST=0.0.0.0:3001
ENV=dev
LOG_LEVEL=debug

DB_DRIVER=postgres
DB_USER=postgres
DB_NAME=postgres
DB_PASSWORD="SLvdfPyb575PUoYzN2p1JqAY=Jo5tu9D"
DB_HOST=*************
DB_PORT=15432

DATABASE_URL="**************************************************************************/postgres?sslmode=disable"
PRISMA_SCHEMA_DISABLE_ADVISORY_LOCK=true

# Slack OAuth Configuration
SLACK_CLIENT_ID=127987402709.9289199587346
SLACK_CLIENT_SECRET=bf665abd642b723402fff3142f661061

FRONTEND_URL=http://localhost:3000
API_URL=http://localhost:3001
