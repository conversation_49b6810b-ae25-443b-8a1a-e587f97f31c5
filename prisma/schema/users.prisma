// user.prisma
enum Role {
  USER
  SUPER_ADMIN
}

model users {
  id           String  @id @default(uuid()) @db.Uuid
  email        String  @unique
  full_name    String?
  display_name String?
  position     String?
  team_code    String?
  avatar_url   String?
  company      String?
  slack_id     String? @unique
  role         Role    @default(USER)
  is_active    <PERSON><PERSON>an @default(true)

  created_at DateTime @default(now())
  updated_at DateTime @default(now()) @updatedAt

  // Relations
  access_tokens    user_tokens[]
  timesheets       timesheets[]
  team             teams?             @relation(fields: [team_code], references: [code], onDelete: SetNull)
  checkins         checkins[]
  user_permissions user_permissions[]

  @@index([email, team_code, role])
}
