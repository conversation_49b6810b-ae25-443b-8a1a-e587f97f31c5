-- Create<PERSON>num
CREATE TYPE "public"."CheckinType" AS ENUM ('OFFICE', 'WFH', 'ONSITE');

-- CreateEnum
CREATE TYPE "public"."Period" AS ENUM ('HALF_MORNING', 'HALF_AFTERNOON', 'FULL_DAY', 'MANY_DAYS');

-- CreateEnum
CREATE TYPE "public"."LeaveType" AS ENUM ('ANNUAL', 'SICK', 'BUSINESS', 'MENSTRUAL', 'BIRTHDAY', 'ORDINATION');

-- CreateEnum
CREATE TYPE "public"."TimesheetType" AS ENUM ('PROJECT', 'SGA', 'LEAVE', 'INTERNAL', 'EXTERNAL', 'OT');

-- CreateEnum
CREATE TYPE "public"."Role" AS ENUM ('USER', 'ADMIN', 'SUPER_ADMIN');

-- CreateTable
CREATE TABLE "public"."checkins" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "type" "public"."CheckinType" NOT NULL,
    "leave_period" "public"."Period" NOT NULL,
    "location" TEXT NOT NULL,
    "remarks" TEXT,
    "is_unused" BOOLEAN NOT NULL DEFAULT false,
    "date" TIME NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "checkins_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."holidays" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "is_national" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "holidays_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."projects" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "projects_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."sgas" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "sgas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."teams" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "start_working_at" TIME,
    "end_working_at" TIME,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "teams_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."timesheets" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "sga_id" UUID,
    "project_code" TEXT,
    "timing" DOUBLE PRECISION NOT NULL,
    "type" "public"."TimesheetType" NOT NULL,
    "leave_type" "public"."LeaveType",
    "description" TEXT,
    "date" TIME NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "timesheets_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."user_tokens" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "token" TEXT NOT NULL,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "device_info" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."users" (
    "id" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "full_name" TEXT,
    "display_name" TEXT,
    "position" TEXT,
    "team_code" TEXT,
    "avatar_url" TEXT,
    "company" TEXT,
    "slack_id" TEXT,
    "role" "public"."Role" NOT NULL DEFAULT 'USER',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "checkins_type_location_idx" ON "public"."checkins"("type", "location");

-- CreateIndex
CREATE INDEX "holidays_name_date_idx" ON "public"."holidays"("name", "date");

-- CreateIndex
CREATE UNIQUE INDEX "projects_name_key" ON "public"."projects"("name");

-- CreateIndex
CREATE UNIQUE INDEX "projects_code_key" ON "public"."projects"("code");

-- CreateIndex
CREATE INDEX "projects_name_code_idx" ON "public"."projects"("name", "code");

-- CreateIndex
CREATE UNIQUE INDEX "sgas_name_key" ON "public"."sgas"("name");

-- CreateIndex
CREATE INDEX "sgas_name_idx" ON "public"."sgas"("name");

-- CreateIndex
CREATE UNIQUE INDEX "teams_name_key" ON "public"."teams"("name");

-- CreateIndex
CREATE UNIQUE INDEX "teams_code_key" ON "public"."teams"("code");

-- CreateIndex
CREATE INDEX "teams_name_code_idx" ON "public"."teams"("name", "code");

-- CreateIndex
CREATE INDEX "timesheets_type_idx" ON "public"."timesheets"("type");

-- CreateIndex
CREATE UNIQUE INDEX "user_tokens_token_key" ON "public"."user_tokens"("token");

-- CreateIndex
CREATE INDEX "user_tokens_user_id_idx" ON "public"."user_tokens"("user_id");

-- CreateIndex
CREATE INDEX "user_tokens_token_idx" ON "public"."user_tokens"("token");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "public"."users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "users_slack_id_key" ON "public"."users"("slack_id");

-- CreateIndex
CREATE INDEX "users_email_team_code_role_idx" ON "public"."users"("email", "team_code", "role");

-- AddForeignKey
ALTER TABLE "public"."checkins" ADD CONSTRAINT "checkins_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."timesheets" ADD CONSTRAINT "timesheets_project_code_fkey" FOREIGN KEY ("project_code") REFERENCES "public"."projects"("code") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."timesheets" ADD CONSTRAINT "timesheets_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."timesheets" ADD CONSTRAINT "timesheets_sga_id_fkey" FOREIGN KEY ("sga_id") REFERENCES "public"."sgas"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_tokens" ADD CONSTRAINT "user_tokens_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."users" ADD CONSTRAINT "users_team_code_fkey" FOREIGN KEY ("team_code") REFERENCES "public"."teams"("code") ON DELETE SET NULL ON UPDATE CASCADE;
