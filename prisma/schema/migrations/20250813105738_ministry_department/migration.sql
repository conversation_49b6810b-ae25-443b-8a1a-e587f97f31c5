-- CreateTable
CREATE TABLE "public"."departments" (
    "id" UUID NOT NULL,
    "ministry_id" UUID NOT NULL,
    "name_th" TEXT NOT NULL,
    "name_en" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "departments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ministries" (
    "id" UUID NOT NULL,
    "name_th" TEXT NOT NULL,
    "name_en" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "ministries_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "departments_name_th_key" ON "public"."departments"("name_th");

-- CreateIndex
CREATE UNIQUE INDEX "departments_name_en_key" ON "public"."departments"("name_en");

-- CreateIndex
CREATE INDEX "departments_ministry_id_idx" ON "public"."departments"("ministry_id");

-- CreateIndex
CREATE INDEX "departments_name_th_idx" ON "public"."departments"("name_th");

-- CreateIndex
CREATE UNIQUE INDEX "ministries_name_th_key" ON "public"."ministries"("name_th");

-- CreateIndex
CREATE UNIQUE INDEX "ministries_name_en_key" ON "public"."ministries"("name_en");

-- CreateIndex
CREATE INDEX "ministries_name_th_idx" ON "public"."ministries"("name_th");

-- AddForeignKey
ALTER TABLE "public"."departments" ADD CONSTRAINT "departments_ministry_id_fkey" FOREIGN KEY ("ministry_id") REFERENCES "public"."ministries"("id") ON DELETE CASCADE ON UPDATE CASCADE;
