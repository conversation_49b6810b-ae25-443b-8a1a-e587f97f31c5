enum Permission {
  NO_ACCESS
  USER
  ADMIN
}

enum PermissionModule {
  CH<PERSON>KIN
  TIMESHEET
  PMO
}

model user_permissions {
  id         String           @id @default(uuid()) @db.Uuid
  user_id    String           @db.Uuid
  permission Permission       @default(USER)
  module     PermissionModule
  created_at DateTime         @default(now())
  updated_at DateTime         @default(now()) @updatedAt

  // Relations
  user users @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, permission, module])
  @@index([user_id])
}
