package timesheet

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/requests"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type TimesheetController struct {
}

func (m TimesheetController) Pagination(c core.IHTTPContext) error {
	input := &requests.CheckinPaginationRequest{}
	if err := c.Bind(input); err != nil {
		ierr := core.Error{
			Status:  http.StatusBadRequest,
			Code:    "INVALID_PARAMS",
			Message: "Invalid params request",
		}
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	if ierr := input.Validate(); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	timesheetSvc := services.NewTimesheetService(c)
	res, ierr := timesheetSvc.Pagination(c.GetPageOptions(), &services.TimesheetPaginationOptions{
		StartDate: input.StartDate,
		EndDate:   input.EndDate,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m TimesheetController) Find(c core.IHTTPContext) error {
	timesheetSvc := services.NewTimesheetService(c)
	timesheet, err := timesheetSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, timesheet)
}

func (m TimesheetController) Create(c core.IHTTPContext) error {
	input := &requests.TimesheetCreate{}
	userID := c.GetUser().ID
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	timesheetSvc := services.NewTimesheetService(c)
	payload := &services.TimesheetCreatePayload{}
	_ = utils.Copy(payload, input)
	timesheet, err := timesheetSvc.Create(payload, userID)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, timesheet)
}

func (m TimesheetController) Update(c core.IHTTPContext) error {
	input := &requests.TimesheetUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	timesheetSvc := services.NewTimesheetService(c)
	payload := &services.TimesheetUpdatePayload{}
	_ = utils.Copy(payload, input)
	timesheet, err := timesheetSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, timesheet)
}

func (m TimesheetController) Delete(c core.IHTTPContext) error {
	timesheetSvc := services.NewTimesheetService(c)
	err := timesheetSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
