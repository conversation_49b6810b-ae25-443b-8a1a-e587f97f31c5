package middleware

import (
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

func AdminMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			cc := c.(*core.HTTPContext)
			// Get authorization header
			authHeader := c.Request().Header.Get("Authorization")
			if authHeader == "" {
				return c.JSON(http.StatusUnauthorized, core.Map{
					"code":    "AUTHORIZATION_TOKEN_REQUIRED",
					"message": "Authorization token required",
				})
			}

			// Extract token from "Bearer <token>" format
			token := authHeader
			if strings.HasPrefix(authHeader, "Bearer ") {
				token = authHeader[7:]
			}

			if token == "" {
				return c.<PERSON>(http.StatusUnauthorized, core.Map{
					"code":    "INVALID_AUTHORIZATION_FORMAT",
					"message": "Invalid authorization format",
				})
			}

			// Validate token and get user
			authSvc := services.NewAuthService(cc)
			user, err := authSvc.ValidateToken(token)
			if err != nil {
				return c.JSON(http.StatusUnauthorized, core.Map{
					"code":    "INVALID_TOKEN",
					"message": "Invalid or expired token",
				})
			}

			if user.Role != models.UserRoleAdmin && user.Role != models.UserRoleSuperAdmin {
				return c.JSON(http.StatusForbidden, core.Map{
					"code":    "FORBIDDEN",
					"message": "You do not have permission to access this resource",
				})
			}

			cc.SetUser(&core.ContextUser{
				ID:      user.ID,
				Email:   user.Email,
				Name:    user.FullName,
				Segment: string(user.Role),
				Data: map[string]string{
					"token":    token,
					"is_admin": "true",
				},
			})

			cc.SetData("user", user)

			return next(c)
		}
	}
}
