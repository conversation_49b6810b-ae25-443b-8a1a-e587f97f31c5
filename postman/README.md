# Finework API - Postman Collections

This directory contains comprehensive Postman collections for all Finework API modules.

## Files

### Complete Collection

- `Finework-API-Complete.postman_collection.json` - Complete collection with all API endpoints
- `Finework-API-Environment.postman_environment.json` - Environment variables for easy configuration

### Individual Module Collections

- `Home-Module.postman_collection.json` - Health check endpoint only
- `Auth-Module.postman_collection.json` - Authentication endpoints only
- `Users-Module.postman_collection.json` - User management endpoints only
- `Teams-Module.postman_collection.json` - Team management endpoints only
- `Holidays-Module.postman_collection.json` - Holiday management endpoints only
- `Me-Profile-Module.postman_collection.json` - Profile management endpoints only

You can import either the complete collection or individual module collections based on your needs.

## Modules Covered

### 1. Home

- **GET** `/` - Health check endpoint

### 2. Authentication

- **GET** `/auth/slack-login` - Initiate Slack OAuth login
- **GET** `/auth/slack-callback` - Handle Slack OAuth callback
- **POST** `/auth/logout` - Logout user (requires authentication)

### 3. Users (All require authentication)

- **GET** `/users` - Get all users with pagination
- **GET** `/users/:id` - Get user by ID
- **POST** `/users` - Create new user
- **PUT** `/users/:id` - Update user
- **DELETE** `/users/:id` - Delete user

### 4. Teams (All require authentication)

- **GET** `/teams` - Get all teams with pagination
- **GET** `/teams/:id` - Get team by ID
- **GET** `/teams/code/:code` - Get team by code
- **POST** `/teams` - Create new team
- **PUT** `/teams/:id` - Update team
- **DELETE** `/teams/:id` - Delete team

### 5. Holidays (All require authentication)

- **GET** `/holidays` - Get all holidays with pagination
- **GET** `/holidays/:id` - Get holiday by ID
- **POST** `/holidays` - Create new holiday
- **PUT** `/holidays/:id` - Update holiday
- **DELETE** `/holidays/:id` - Delete holiday

### 6. Me (Profile) (All require authentication)

- **GET** `/me` - Get current user profile
- **PUT** `/me` - Update current user profile
- **GET** `/me/devices` - Get current user's devices/tokens
- **DELETE** `/me/devices/:id` - Delete a device/token

## Setup Instructions

### 1. Import Collections

1. Open Postman
2. Click "Import" button
3. Select both JSON files:
   - `Finework-API-Complete.postman_collection.json`
   - `Finework-API-Environment.postman_environment.json`

### 2. Configure Environment

1. Select "Finework API Environment" from the environment dropdown
2. Update the following variables:
   - `baseUrl`: Your API base URL (default: `http://localhost:3000`)
   - `token`: Your authentication token (obtain via Slack login)

### 3. Authentication Flow

1. **Option 1: Slack OAuth (Recommended)**

   - Use the "Slack Login" endpoint to get the OAuth URL
   - Complete OAuth flow in browser
   - Extract token from callback URL
   - Set the token in environment variables

2. **Option 2: Manual Token**
   - If you have a valid token, set it directly in the environment

## Request Body Examples

### Create User

```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "full_name": "John Doe",
  "display_name": "John",
  "position": "Software Developer",
  "team_code": "DEV",
  "avatar_url": "https://example.com/avatar.jpg",
  "role": "USER"
}
```

### Create Team

```json
{
  "name": "Development Team",
  "code": "DEV",
  "description": "Software development team",
  "start_working_at": "2024-01-01T09:00:00Z",
  "end_working_at": "2024-01-01T18:00:00Z"
}
```

### Create Holiday

```json
{
  "name": "New Year's Day",
  "date": "2024-01-01T00:00:00Z",
  "is_national": true
}
```

### Update Profile (Me)

```json
{
  "full_name": "Updated Name",
  "display_name": "Updated Display",
  "position": "Updated Position",
  "team_code": "DEV",
  "avatar_url": "https://example.com/updated-avatar.jpg"
}
```

## Environment Variables

| Variable      | Description               | Example                 |
| ------------- | ------------------------- | ----------------------- |
| `baseUrl`     | API base URL              | `http://localhost:3000` |
| `token`       | Authentication token      | `your-jwt-token-here`   |
| `page`        | Pagination page number    | `1`                     |
| `limit`       | Pagination limit          | `10`                    |
| `user_id`     | User ID for operations    | `uuid-here`             |
| `team_id`     | Team ID for operations    | `uuid-here`             |
| `team_code`   | Team code                 | `DEV`                   |
| `holiday_id`  | Holiday ID for operations | `uuid-here`             |
| `device_id`   | Device/Token ID           | `uuid-here`             |
| `slack_code`  | Slack OAuth code          | `oauth-code`            |
| `slack_state` | Slack OAuth state         | `state-value`           |

## User Roles

The API supports three user roles:

- `USER` - Regular user
- `ADMIN` - Administrator
- `SUPER_ADMIN` - Super administrator

## Notes

1. **Authentication**: Most endpoints require a valid JWT token in the Authorization header
2. **Pagination**: List endpoints support `page` and `limit` query parameters
3. **Validation**: All create/update endpoints validate input data
4. **Time Format**: Use ISO 8601 format for dates and times
5. **UUIDs**: All IDs are UUIDs

## Testing Workflow

1. Start with the health check endpoint to verify API connectivity
2. Use Slack login to authenticate and get a token
3. Test user management endpoints
4. Test team management endpoints
5. Test holiday management endpoints
6. Test profile management endpoints

## Troubleshooting

- **401 Unauthorized**: Check if your token is valid and properly set
- **404 Not Found**: Verify the endpoint URL and resource IDs
- **422 Validation Error**: Check request body format and required fields
- **500 Internal Server Error**: Check API logs for detailed error information
