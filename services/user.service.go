package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models" // Importing the models package
	"gitlab.finema.co/finema/finework/finework-api/repo"   // Importing the repo package
	core "gitlab.finema.co/finema/idin-core"               // Importing the core package
	"gitlab.finema.co/finema/idin-core/repository"         // Importing the repository package
	"gitlab.finema.co/finema/idin-core/utils"
)

type IUserService interface { // Defining the IUserService interface
	Create(input *UserCreatePayload) (*models.User, core.IError)                                 // Method declaration for creating a user
	Update(id string, input *UserUpdatePayload) (*models.User, core.IError)                      // Method declaration for updating a user
	Find(id string) (*models.User, core.IError)                                                  // Method declaration for finding a user
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.User], core.IError) // Method declaration for pagination of users
	Delete(id string) core.IError                                                                // Method declaration for deleting a user
}

type userService struct {
	ctx core.IContext // Struct for userService with a context field
}

func (s userService) Create(input *UserCreatePayload) (*models.User, core.IError) {
	// Set default role if not provided
	role := models.UserRole(input.Role)
	if role == "" {
		role = models.UserRoleUser
	}

	// Implementation for creating a user
	user := &models.User{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		Email:               input.Email,
		FullName:            input.FullName,
		DisplayName:         input.DisplayName,
		Position:            input.Position,
		TeamCode:            utils.ToPointer(input.TeamCode),
		AvatarURL:           input.AvatarURL,
		Role:                role,
	}

	ierr := repo.User(s.ctx).Create(user) // Calling the Create method of the repo.User repository
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Create method fails
	}

	return s.Find(user.ID) // Calling the Find method to retrieve the created user
}

func (s userService) Update(id string, input *UserUpdatePayload) (*models.User, core.IError) {
	// Implementation for updating a user
	user, ierr := s.Find(id) // Calling the Find method to retrieve the user to update
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Find method fails
	}

	// Update fields if provided
	if input.FullName != "" {
		user.FullName = input.FullName
	}
	if input.DisplayName != "" {
		user.DisplayName = input.DisplayName
	}
	if input.Position != "" {
		user.Position = input.Position
	}
	if input.TeamCode != "" {
		user.TeamCode = utils.ToPointer(input.TeamCode)
	}
	if input.AvatarURL != "" {
		user.AvatarURL = input.AvatarURL
	}
	if input.Role != "" {
		user.Role = models.UserRole(input.Role)
	}

	ierr = repo.User(s.ctx).Where("id = ?", id).Updates(user) // Calling the Updates method of the repo.User repository
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Updates method fails
	}

	return s.Find(user.ID) // Calling the Find method to retrieve the updated user
}

func (s userService) Find(id string) (*models.User, core.IError) {
	// Implementation for finding a user
	return repo.User(s.ctx).FindOne("id = ?", id) // Calling the FindOne method of the repo.User repository
}

func (s userService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.User], core.IError) {
	// Implementation for user pagination
	return repo.User(s.ctx, repo.UserOrderBy(pageOptions)).Pagination(pageOptions) // Calling the Pagination method of the repo.User repository
}

func (s userService) Delete(id string) core.IError {
	// Implementation for deleting a user
	_, ierr := s.Find(id) // Calling the Find method to check if the user exists
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr) // Returning an error if the Find method fails
	}

	return repo.User(s.ctx).Delete("id = ?", id) // Calling the Delete method of the repo.User repository
}

func NewUserService(ctx core.IContext) IUserService {
	return &userService{ctx: ctx} // Creating a new instance of userService with the provided context
}
