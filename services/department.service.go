package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IDepartmentService interface {
	Create(input *DepartmentCreatePayload) (*models.Department, core.IError)
	Update(id string, input *DepartmentUpdatePayload) (*models.Department, core.IError)
	Find(id string) (*models.Department, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Department], core.IError)
	Delete(id string) core.IError
}

type departmentService struct {
	ctx core.IContext
}

func (s departmentService) Create(input *DepartmentCreatePayload) (*models.Department, core.IError) {
	department := &models.Department{
		BaseModel:  models.NewBaseModel(),
		MinistryID: input.MinistryID,
		NameTh:     input.NameTh,
		NameEn:     utils.ToPointer(input.NameEn),
	}

	ierr := repo.Department(s.ctx).Create(department)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(department.ID)
}

func (s departmentService) Update(id string, input *DepartmentUpdatePayload) (*models.Department, core.IError) {
	department, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.MinistryID != "" {
		department.MinistryID = input.MinistryID
	}

	if input.NameTh != "" {
		department.NameTh = input.NameTh
	}

	if input.NameEn != "" {
		department.NameEn = utils.ToPointer(input.NameEn)
	}

	ierr = repo.Department(s.ctx).Where("id = ?", id).Updates(department)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(department.ID)
}

func (s departmentService) Find(id string) (*models.Department, core.IError) {
	return repo.Department(s.ctx, repo.DepartmentWithMinistry()).FindOne("id = ?", id)
}

func (s departmentService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Department], core.IError) {
	return repo.Department(s.ctx, repo.DepartmentWithMinistry(), repo.DepartmentOrderBy(pageOptions)).Pagination(pageOptions)
}

func (s departmentService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Department(s.ctx).Delete("id = ?", id)
}

func NewDepartmentService(ctx core.IContext) IDepartmentService {
	return &departmentService{ctx: ctx}
}
