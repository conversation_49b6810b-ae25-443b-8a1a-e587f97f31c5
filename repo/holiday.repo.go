package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type HolidayOption func(repository.IRepository[models.Holiday])

var Holiday = func(c core.IContext, options ...HolidayOption) repository.IRepository[models.Holiday] {
	r := repository.New[models.Holiday](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func HolidayOrderBy(pageOptions *core.PageOptions) HolidayOption {
	return func(c repository.IRepository[models.Holiday]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("date DESC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}
