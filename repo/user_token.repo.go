package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type UserTokenOption func(repository.IRepository[models.UserToken])

var UserToken = func(c core.IContext, options ...UserTokenOption) repository.IRepository[models.UserToken] {
	r := repository.New[models.UserToken](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func UserTokenOrderBy(pageOptions *core.PageOptions) UserTokenOption {
	return func(c repository.IRepository[models.UserToken]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}
