### Test CRUD Operations for Holidays, Teams, and Users

### Variables
@baseUrl = http://localhost:3000
@token = YOUR_AUTH_TOKEN_HERE

### ===== HOLIDAYS CRUD =====

### Create Holiday
POST {{baseUrl}}/holidays
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "New Year's Day",
  "date": "2024-01-01T00:00:00Z",
  "is_national": true
}

### Get All Holidays (Pagination)
GET {{baseUrl}}/holidays
Authorization: Bearer {{token}}

### Get Holiday by ID
GET {{baseUrl}}/holidays/HOLIDAY_ID_HERE
Authorization: Bearer {{token}}

### Update Holiday
PUT {{baseUrl}}/holidays/HOLIDAY_ID_HERE
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "New Year's Day (Updated)",
  "is_national": false
}

### Delete Holiday
DELETE {{baseUrl}}/holidays/HOLIDAY_ID_HERE
Authorization: Bearer {{token}}

### ===== TEAMS CRUD =====

### Create Team
POST {{baseUrl}}/teams
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "Development Team",
  "code": "DEV",
  "description": "Software development team",
  "start_working_at": "09:00:00",
  "end_working_at": "18:00:00"
}

### Get All Teams (Pagination)
GET {{baseUrl}}/teams
Authorization: Bearer {{token}}

### Get Team by ID
GET {{baseUrl}}/teams/TEAM_ID_HERE
Authorization: Bearer {{token}}

### Get Team by Code
GET {{baseUrl}}/teams/code/DEV
Authorization: Bearer {{token}}

### Update Team
PUT {{baseUrl}}/teams/TEAM_ID_HERE
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "Development Team (Updated)",
  "description": "Updated software development team"
}

### Delete Team
DELETE {{baseUrl}}/teams/TEAM_ID_HERE
Authorization: Bearer {{token}}

### ===== USERS CRUD =====

### Create User
POST {{baseUrl}}/users
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "email": "<EMAIL>",
  "full_name": "Test User",
  "display_name": "Test",
  "position": "Developer",
  "team_code": "DEV",
  "role": "USER"
}

### Get All Users (Pagination)
GET {{baseUrl}}/users
Authorization: Bearer {{token}}

### Get User by ID
GET {{baseUrl}}/users/USER_ID_HERE
Authorization: Bearer {{token}}

### Update User
PUT {{baseUrl}}/users/USER_ID_HERE
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "full_name": "Test User (Updated)",
  "position": "Senior Developer"
}

### Delete User
DELETE {{baseUrl}}/users/USER_ID_HERE
Authorization: Bearer {{token}}

### ===== ME ENDPOINTS (Profile Management) =====

### Get My Profile
GET {{baseUrl}}/me
Authorization: Bearer {{token}}

### Update My Profile
PUT {{baseUrl}}/me
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "full_name": "Updated Name",
  "display_name": "Updated Display",
  "position": "Updated Position",
  "team_code": "DEV"
}

### Get My Devices
GET {{baseUrl}}/me/devices
Authorization: Bearer {{token}}

### Delete My Device
DELETE {{baseUrl}}/me/devices/DEVICE_ID_HERE
Authorization: Bearer {{token}}
