package models

import "time"

type CheckinType string

const (
	CheckinTypeOffice CheckinType = "OFFICE"
	CheckinTypeWfh    CheckinType = "WFH"
	CheckinTypeOnsite CheckinType = "ONSITE"
)

type LeavePeriod string

const (
	LeavePeriodHalfMorning   LeavePeriod = "HALF_MORNING"
	LeavePeriodHalfAfternoon LeavePeriod = "HALF_AFTERNOON"
	LeavePeriodFullDay       LeavePeriod = "FULL_DAY"
	LeavePeriodManyDays      LeavePeriod = "MANY_DAYS"
)

type Checkin struct {
	BaseModelHardDelete
	UserId      string      `json:"user_id" gorm:"column:user_id;type:uuid"`
	Type        CheckinType `json:"type" gorm:"column:type"`
	LeavePeriod LeavePeriod `json:"leave_period" gorm:"column:leave_period"`
	Location    *string     `json:"location" gorm:"column:location"`
	Remarks     *string     `json:"remarks" gorm:"column:remarks"`
	IsUnused    *bool       `json:"is_unused" gorm:"column:is_unused;default:false"`
	Date        *time.Time  `json:"date" gorm:"column:date;type:date"`
	// Relations
	User User `json:"user" gorm:"foreignKey:UserId;references:ID;constraint:OnDelete:CASCADE"`
}

func (Checkin) TableName() string {
	return "checkins"
}
