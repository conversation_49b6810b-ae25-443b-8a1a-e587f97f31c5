package models

type UserPermissionType string

const (
	UserPermissionNoAccess UserPermissionType = "NO_ACCESS"
	UserPermissionUser     UserPermissionType = "USER"
	UserPermissionAdmin    UserPermissionType = "ADMIN"
)

type PermissionModuleType string

const (
	PermissionModuleCheckin   PermissionModuleType = "CHECKIN"
	PermissionModuleTimesheet PermissionModuleType = "TIMESHEET"
	PermissionModulePmo       PermissionModuleType = "PMO"
)

type UserPermission struct {
	BaseModelHardDelete
	UserID     string               `json:"user_id" gorm:"column:user_id;type:uuid"`
	Permission UserPermissionType   `json:"permission" gorm:"column:permission"`
	Module     PermissionModuleType `json:"module" gorm:"column:module"`

	// Relations
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID;constraint:OnDelete:CASCADE"`
}

func (UserPermission) TableName() string {
	return "user_permissions"
}
