package models

type UserRole string

const (
	UserRoleUser       UserRole = "USER"
	UserRoleAdmin      UserRole = "ADMIN"
	UserRoleSuperAdmin UserRole = "SUPER_ADMIN"
)

type User struct {
	BaseModelHardDelete
	Email       string   `json:"email" gorm:"column:email"`
	FullName    string   `json:"full_name" gorm:"column:full_name"`
	DisplayName string   `json:"display_name" gorm:"column:display_name"`
	Position    string   `json:"position" gorm:"column:position"`
	TeamCode    *string  `json:"team_code" gorm:"column:team_code"`
	Company     *string  `json:"company" gorm:"column:company"`
	AvatarURL   string   `json:"avatar_url" gorm:"column:avatar_url"`
	SlackID     *string  `json:"slack_id" gorm:"column:slack_id"`
	Role        UserRole `json:"role" gorm:"column:role"`
	IsActive    bool     `json:"is_active" gorm:"column:is_active"`

	Permissions []UserPermission `json:"permissions" gorm:"foreignKey:UserID;references:ID"`
}

func (User) TableName() string {
	return "users"
}
